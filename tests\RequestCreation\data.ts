// tests/RequestCreation/data.ts

export interface RequestCreationData {
  auth: {
    signinUrl: string;
    email: string;
    password: string;
  };
  selectors: {
    dashboardTile: string;
    projectBasedLink: { role: 'link'; name: string };
    addButton: { role: 'button'; name: string };
    mobileInput: { role: 'textbox'; name: string };
    nameInputA: { role: 'textbox'; name: string }; // "Start typing customer name..."
    nameInputB: { role: 'textbox'; name: string }; // "* Name"
    descriptionInput: { role: 'textbox'; name: string };
    priorityCombo: { role: 'combobox'; name: string };
    priorityNormalTitle: string; // getByTitle('Normal')
    submitButton: { role: 'button'; name: string };
  };
  testData: {
    mobile: string;
    nameWhenA: string;
    nameWhenB: string;
    description: string;
  };
}

const RequestCreationData: RequestCreationData = {
  auth: {
    signinUrl: 'https://qa05.wify.co.in/signin',
    email: '<EMAIL>',
    password: 'test',
  },
  selectors: {
    dashboardTile:
      'div:nth-child(2) > .ant-col > .ant-list-item > .ant-list-item-meta > .ant-list-item-meta-content > .ant-list-item-meta-title > .gx-position-relative > .wy-gap-0 > div',
    projectBasedLink: { role: 'link', name: ' Project based' },
    addButton: { role: 'button', name: 'add' },
    mobileInput: { role: 'textbox', name: 'Mobile(+91)' },
    nameInputA: { role: 'textbox', name: 'Start typing customer name...' },
    nameInputB: { role: 'textbox', name: '* Name' },
    descriptionInput: { role: 'textbox', name: '* Description' },
    priorityCombo: { role: 'combobox', name: '* Priority' },
    priorityNormalTitle: 'Normal',
    submitButton: { role: 'button', name: 'Submit' },
  },
  testData: {
    mobile: '8850852329',
    nameWhenA: 'Amarr',
    nameWhenB: 'Tufel',
    description: 'PLAWRIGHT 102',
  },
};

export default RequestCreationData;
