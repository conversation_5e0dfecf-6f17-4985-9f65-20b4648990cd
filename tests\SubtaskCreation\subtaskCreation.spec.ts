import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://qa01.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Email' }).press('Tab');
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('textbox', { name: 'Password' }).press('Enter');
  await page.getByRole('button', { name: 'Sign In' }).click();
  await page.locator('div:nth-child(2) > .ant-col > .ant-list-item > .ant-list-item-meta > .ant-list-item-meta-content > .ant-list-item-meta-title > .gx-position-relative > .wy-gap-0 > div').first().click();
  await page.getByRole('link', { name: ' Installations' }).click();
  await page.getByRole('paragraph').filter({ hasText: 'INST250813109460(Urgent)' }).click();
  await page.getByRole('button', { name: 'user-add Add subtask' }).click();
  await page.waitForTimeout(4000);
  await page.getByText('Visit', { exact: true }).click();
  await page.getByRole('textbox', { name: '* zStart date ( No requested' }).click();
  await page.getByText('20', { exact: true }).click();
  await page.getByRole('combobox', { name: '* Start Time' }).click();
  await page.getByTitle('9:45AM').click();
  await page.getByRole('combobox', { name: '* End Time' }).click();
  await page.getByText('10:45AM').nth(2).click();
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.locator('div:nth-child(8) > .ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector > .ant-select-selection-overflow').click();
  await page.getByRole('combobox', { name: '* Assignee' }).fill('t');
  await page.getByText('boult (boult)').click();
  await page.getByText('PriorityNormalRemarks').click();
  await page.getByRole('textbox', { name: '* Remarks' }).click();
  await page.getByRole('textbox', { name: '* Remarks' }).fill('Test Subtask');
  await page.getByRole('button', { name: 'Create' }).click();
  await page.getByRole('link', { name: ' Visit' }).click();
  await page.getByRole('button', { name: 'View' }).click();
});